package discovery

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"go-embed-nats-dist/internal/config"
)

type SeedAnnouncement struct {
	ServerID    string    `json:"server_id"`
	ClusterName string    `json:"cluster_name"`
	Host        string    `json:"host"`
	NATSPort    int       `json:"nats_port"`
	ClusterPort int       `json:"cluster_port"`
	Timestamp   time.Time `json:"timestamp"`
	Version     string    `json:"version"`
}

type Peer struct {
	ServerID    string
	Host        string
	NATSPort    int
	ClusterPort int
	LastSeen    time.Time
}

type BroadcastDiscovery struct {
	config      *config.Config
	serverID    string
	localIP     string
	peers       map[string]*Peer
	peersMutex  sync.RWMutex
	stopChan    chan struct{}
	onPeerFound func([]Peer)
}

func NewBroadcastDiscovery(cfg *config.Config, serverID string, onPeerFound func([]Peer)) *BroadcastDiscovery {
	localIP := getLocalIP()

	return &BroadcastDiscovery{
		config:      cfg,
		serverID:    serverID,
		localIP:     localIP,
		peers:       make(map[string]*Peer),
		stopChan:    make(chan struct{}),
		onPeerFound: onPeerFound,
	}
}

func (bd *BroadcastDiscovery) Start() error {
	log.Printf("Starting broadcast discovery on port %d", bd.config.Discovery.Broadcast.Port)

	// Start the announcer
	go bd.startAnnouncer()

	// Start the listener
	go bd.startListener()

	// Start peer cleanup routine
	go bd.startPeerCleanup()

	return nil
}

func (bd *BroadcastDiscovery) Stop() {
	close(bd.stopChan)
}

func (bd *BroadcastDiscovery) GetPeers() []Peer {
	bd.peersMutex.RLock()
	defer bd.peersMutex.RUnlock()

	peers := make([]Peer, 0, len(bd.peers))
	for _, peer := range bd.peers {
		peers = append(peers, *peer)
	}

	return peers
}

func (bd *BroadcastDiscovery) startAnnouncer() {
	ticker := time.NewTicker(bd.config.Discovery.Broadcast.Interval)
	defer ticker.Stop()

	for {
		select {
		case <-bd.stopChan:
			return
		case <-ticker.C:
			bd.broadcastAnnouncement()
		}
	}
}

func (bd *BroadcastDiscovery) broadcastAnnouncement() {
	announcement := SeedAnnouncement{
		ServerID:    bd.serverID,
		ClusterName: bd.config.NATS.ClusterName,
		Host:        bd.localIP,
		NATSPort:    bd.config.NATS.ClientPort,
		ClusterPort: bd.config.NATS.ClusterPort,
		Timestamp:   time.Now(),
		Version:     bd.config.App.Version,
	}

	data, err := json.Marshal(announcement)
	if err != nil {
		log.Printf("Error marshaling announcement: %v", err)
		return
	}

	// Broadcast to subnet
	bd.sendBroadcast(data, "***************")

	// Also try multicast
	bd.sendBroadcast(data, "*********")
}

func (bd *BroadcastDiscovery) sendBroadcast(data []byte, address string) {
	addr := fmt.Sprintf("%s:%d", address, bd.config.Discovery.Broadcast.Port)

	conn, err := net.Dial("udp", addr)
	if err != nil {
		if bd.config.App.Debug {
			log.Printf("Error creating UDP connection to %s: %v", addr, err)
		}
		return
	}
	defer conn.Close()

	_, err = conn.Write(data)
	if err != nil {
		if bd.config.App.Debug {
			log.Printf("Error sending broadcast to %s: %v", addr, err)
		}
	}
}

func (bd *BroadcastDiscovery) startListener() {
	addr, err := net.ResolveUDPAddr("udp", fmt.Sprintf(":%d", bd.config.Discovery.Broadcast.Port))
	if err != nil {
		log.Printf("Error resolving UDP address: %v", err)
		return
	}

	conn, err := net.ListenUDP("udp", addr)
	if err != nil {
		log.Printf("Error listening on UDP port %d: %v", bd.config.Discovery.Broadcast.Port, err)
		return
	}
	defer conn.Close()

	log.Printf("Listening for peer announcements on UDP port %d", bd.config.Discovery.Broadcast.Port)

	buffer := make([]byte, 1024)

	for {
		select {
		case <-bd.stopChan:
			return
		default:
			conn.SetReadDeadline(time.Now().Add(1 * time.Second))
			n, remoteAddr, err := conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					continue
				}
				log.Printf("Error reading UDP message: %v", err)
				continue
			}

			bd.handleAnnouncement(buffer[:n], remoteAddr)
		}
	}
}

func (bd *BroadcastDiscovery) handleAnnouncement(data []byte, remoteAddr *net.UDPAddr) {
	var announcement SeedAnnouncement
	if err := json.Unmarshal(data, &announcement); err != nil {
		if bd.config.App.Debug {
			log.Printf("Error unmarshaling announcement: %v", err)
		}
		return
	}

	// Ignore our own announcements
	if announcement.ServerID == bd.serverID {
		return
	}

	// Ignore announcements from different clusters
	if announcement.ClusterName != bd.config.NATS.ClusterName {
		return
	}

	// Update peer information
	bd.peersMutex.Lock()
	peer := &Peer{
		ServerID:    announcement.ServerID,
		Host:        announcement.Host,
		NATSPort:    announcement.NATSPort,
		ClusterPort: announcement.ClusterPort,
		LastSeen:    time.Now(),
	}

	isNewPeer := bd.peers[announcement.ServerID] == nil
	bd.peers[announcement.ServerID] = peer
	bd.peersMutex.Unlock()

	if isNewPeer {
		log.Printf("Discovered new peer: %s (%s:%d)",
			announcement.ServerID, announcement.Host, announcement.ClusterPort)

		// Notify about peer discovery
		if bd.onPeerFound != nil {
			bd.onPeerFound(bd.GetPeers())
		}
	}
}

func (bd *BroadcastDiscovery) startPeerCleanup() {
	ticker := time.NewTicker(bd.config.Discovery.Broadcast.Timeout)
	defer ticker.Stop()

	for {
		select {
		case <-bd.stopChan:
			return
		case <-ticker.C:
			bd.cleanupStaleePeers()
		}
	}
}

func (bd *BroadcastDiscovery) cleanupStaleePeers() {
	bd.peersMutex.Lock()
	defer bd.peersMutex.Unlock()

	cutoff := time.Now().Add(-bd.config.Discovery.Broadcast.Timeout)

	for serverID, peer := range bd.peers {
		if peer.LastSeen.Before(cutoff) {
			log.Printf("Removing stale peer: %s", serverID)
			delete(bd.peers, serverID)
		}
	}
}

func getLocalIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}
