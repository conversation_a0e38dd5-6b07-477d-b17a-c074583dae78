package nats

import (
	"fmt"
	"log"
	"net/url"
	"sort"

	"go-embed-nats-dist/internal/discovery"
)

type ClusterManager struct {
	natsServer *EmbeddedNATS
	discovery  *discovery.BroadcastDiscovery
}

func NewClusterManager(natsServer *EmbeddedNATS, discovery *discovery.BroadcastDiscovery) *ClusterManager {
	return &ClusterManager{
		natsServer: natsServer,
		discovery:  discovery,
	}
}

func (cm *ClusterManager) Start() error {
	// Start discovery
	if err := cm.discovery.Start(); err != nil {
		return fmt.Errorf("failed to start discovery: %w", err)
	}

	log.Println("Cluster manager started")
	return nil
}

func (cm *ClusterManager) Stop() {
	if cm.discovery != nil {
		cm.discovery.Stop()
	}
}

func (cm *ClusterManager) HandlePeerDiscovery(peers []discovery.Peer) {
	if len(peers) == 0 {
		return
	}

	log.Printf("Updating cluster with %d peers", len(peers))

	// Convert peers to NATS routes
	routes := cm.peersToRoutes(peers)

	// Update NATS server routes
	if err := cm.natsServer.UpdateRoutes(routes); err != nil {
		log.Printf("Error updating NATS routes: %v", err)
	}
}

func (cm *ClusterManager) peersToRoutes(peers []discovery.Peer) []*url.URL {
	// Sort peers by ServerID for deterministic ordering
	sort.Slice(peers, func(i, j int) bool {
		return peers[i].ServerID < peers[j].ServerID
	})

	routes := make([]*url.URL, 0, len(peers))

	for _, peer := range peers {
		routeURL := fmt.Sprintf("nats://%s:%d", peer.Host, peer.ClusterPort)
		if parsedURL, err := url.Parse(routeURL); err == nil {
			routes = append(routes, parsedURL)
		} else {
			log.Printf("Error parsing route URL %s: %v", routeURL, err)
		}
	}

	return routes
}

func (cm *ClusterManager) GetClusterInfo() map[string]interface{} {
	peers := cm.discovery.GetPeers()

	return map[string]interface{}{
		"server_id":   cm.natsServer.GetServerID(),
		"peer_count":  len(peers),
		"peers":       peers,
		"client_url":  cm.natsServer.GetClientURL(),
		"cluster_url": cm.natsServer.GetClusterURL(),
	}
}
