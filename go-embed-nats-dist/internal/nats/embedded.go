package nats

import (
	"fmt"
	"log"
	"net/url"
	"os"
	"path/filepath"
	"time"

	"go-embed-nats-dist/internal/config"

	"github.com/google/uuid"
	"github.com/nats-io/nats-server/v2/server"
)

type EmbeddedNATS struct {
	server   *server.Server
	config   *config.Config
	serverID string
	routes   []*url.URL
}

func NewEmbeddedNATS(cfg *config.Config) *EmbeddedNATS {
	return &EmbeddedNATS{
		config:   cfg,
		serverID: generateServerID(),
	}
}

func (e *EmbeddedNATS) Start() error {
	opts := e.createServerOptions()

	var err error
	e.server, err = server.NewServer(opts)
	if err != nil {
		return fmt.Errorf("failed to create NATS server: %w", err)
	}

	if e.config.App.Debug {
		e.server.ConfigureLogger()
	}

	// Start the server in a goroutine
	go e.server.Start()

	// Wait for server to be ready
	if !e.server.ReadyForConnections(10 * time.Second) {
		return fmt.Errorf("NATS server failed to start within timeout")
	}

	log.Printf("NATS server started on port %d (cluster: %d)",
		e.config.NATS.ClientPort, e.config.NATS.ClusterPort)

	return nil
}

func (e *EmbeddedNATS) Stop() error {
	if e.server != nil {
		e.server.Shutdown()
		e.server.WaitForShutdown()
	}
	return nil
}

func (e *EmbeddedNATS) GetClientURL() string {
	return fmt.Sprintf("nats://127.0.0.1:%d", e.config.NATS.ClientPort)
}

func (e *EmbeddedNATS) GetClusterURL() string {
	return fmt.Sprintf("nats://127.0.0.1:%d", e.config.NATS.ClusterPort)
}

func (e *EmbeddedNATS) GetServerID() string {
	return e.serverID
}

func (e *EmbeddedNATS) UpdateRoutes(routes []*url.URL) error {
	e.routes = routes

	if e.server != nil && len(routes) > 0 {
		// Reload server configuration with new routes
		opts := e.createServerOptions()
		return e.server.ReloadOptions(opts)
	}

	return nil
}

func (e *EmbeddedNATS) createServerOptions() *server.Options {
	// Ensure store directory exists
	storeDir := e.config.NATS.JetStream.StoreDir
	if err := os.MkdirAll(storeDir, 0755); err != nil {
		log.Printf("Warning: failed to create store directory %s: %v", storeDir, err)
		storeDir = filepath.Join(os.TempDir(), "jetstream", e.serverID)
		os.MkdirAll(storeDir, 0755)
	}

	opts := &server.Options{
		ServerName: fmt.Sprintf("%s-%s", e.config.App.Name, e.serverID),
		Host:       "0.0.0.0",
		Port:       e.config.NATS.ClientPort,

		// JetStream Configuration
		JetStream:          e.config.NATS.JetStream.Enabled,
		JetStreamMaxMemory: e.config.NATS.JetStream.MaxMemory,
		JetStreamMaxStore:  e.config.NATS.JetStream.MaxFile,
		StoreDir:           storeDir,

		// Cluster Configuration
		Cluster: server.ClusterOpts{
			Name:        e.config.NATS.ClusterName,
			Host:        "0.0.0.0",
			Port:        e.config.NATS.ClusterPort,
			NoAdvertise: false,
		},

		// Performance Tuning
		WriteDeadline: 2 * time.Second,
		MaxPayload:    1024 * 1024,       // 1MB
		MaxPending:    256 * 1024 * 1024, // 256MB
		MaxConn:       10000,
		MaxSubs:       100000,

		// Logging
		Debug: e.config.App.Debug,
		Trace: false,
		NoLog: !e.config.App.Debug,
	}

	// Add routes if available
	if len(e.routes) > 0 {
		opts.Routes = e.routes
	}

	return opts
}

func generateServerID() string {
	return uuid.New().String()[:8]
}
