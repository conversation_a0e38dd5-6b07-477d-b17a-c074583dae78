version: '3.8'

services:
  app:
    build: .
    environment:
      - DISCOVERY_METHOD=broadcast
      - CLUSTER_NAME=docker-cluster
      - BROADCAST_PORT=7222
      - JETSTREAM_STORE_DIR=/data/jetstream
      - DEBUG=true
    networks:
      - nats-cluster
    ports:
      - "8080-8089:8080"  # HTTP API (will map to available ports)
      - "4222-4231:4222"  # NATS client
      - "6222-6231:6222"  # NATS cluster
    volumes:
      - ./data:/data
    deploy:
      replicas: 3
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 30s

networks:
  nats-cluster:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  data:
