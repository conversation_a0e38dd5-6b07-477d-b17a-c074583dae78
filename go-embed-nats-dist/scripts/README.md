# NATS Cluster Testing Scripts

This directory contains comprehensive testing scripts for the Docker Compose NATS cluster setup. These scripts help verify cluster auto-formation, message distribution, and overall system health.

## Scripts Overview

### 1. `test-docker-compose.sh` - Complete Docker Compose Test Suite
**Purpose**: Full end-to-end testing of the Docker Compose setup including build, startup, and cluster testing.

**Features**:
- Builds Docker image from scratch
- Starts services with configurable replica count
- Waits for all services to be healthy
- Runs comprehensive cluster tests
- Provides detailed status information
- Automatic cleanup on completion

**Usage**:
```bash
# Run complete test suite
./scripts/test-docker-compose.sh

# Skip building (use existing image)
./scripts/test-docker-compose.sh --skip-build

# Keep services running after test
./scripts/test-docker-compose.sh --no-cleanup

# Test with different replica count
./scripts/test-docker-compose.sh --replicas 5

# Show container logs at the end
./scripts/test-docker-compose.sh --show-logs
```

### 2. `test-cluster.sh` - Cluster Formation and Health Tests
**Purpose**: Tests cluster formation, peer discovery, and basic messaging functionality.

**Features**:
- Waits for all services to be healthy
- Verifies cluster formation with peer discovery
- Tests broadcast message distribution
- Validates message routing between nodes
- Provides detailed health checks for each node

**Usage**:
```bash
# Run cluster tests (requires running services)
./scripts/test-cluster.sh
```

**Prerequisites**: Services must be running via `docker-compose up -d --scale app=3`

### 3. `test-message-distribution.sh` - Message Distribution Tests
**Purpose**: Comprehensive testing of message distribution across all cluster nodes.

**Features**:
- Tests broadcast message distribution to all nodes
- Verifies regular message routing
- Tests cluster resilience under load
- Analyzes message reception statistics
- Provides detailed per-node analysis

**Usage**:
```bash
# Run all message distribution tests
./scripts/test-message-distribution.sh

# Test only broadcast distribution
./scripts/test-message-distribution.sh --broadcast-only

# Test only message routing
./scripts/test-message-distribution.sh --routing-only

# Test only cluster resilience
./scripts/test-message-distribution.sh --resilience-only

# Customize test parameters
./scripts/test-message-distribution.sh --messages 20 --duration 60
```

### 4. `monitor-cluster.sh` - Real-time Cluster Monitoring
**Purpose**: Real-time monitoring of cluster status, health, and formation.

**Features**:
- Real-time cluster status display
- Node health monitoring
- Peer count tracking
- Optional log display
- Compact and full display modes
- Test message sending capability

**Usage**:
```bash
# Start monitoring with default settings
./scripts/monitor-cluster.sh

# Monitor with custom refresh interval
./scripts/monitor-cluster.sh --interval 10

# Include recent logs in display
./scripts/monitor-cluster.sh --logs

# Use compact display mode
./scripts/monitor-cluster.sh --compact

# Send a test message and exit
./scripts/monitor-cluster.sh --test-message
```

### 5. `test-local.sh` - Local Multi-node Testing
**Purpose**: Tests local multi-node setup without Docker (existing script).

**Usage**:
```bash
./scripts/test-local.sh
```

## Quick Start Guide

### 1. Complete Test Run
```bash
# Build, start, test, and cleanup everything
./scripts/test-docker-compose.sh
```

### 2. Manual Testing Workflow
```bash
# Start services
docker-compose up -d --scale app=3

# Monitor cluster formation
./scripts/monitor-cluster.sh --logs &

# Wait for cluster to form, then test
./scripts/test-cluster.sh

# Test message distribution
./scripts/test-message-distribution.sh

# Cleanup
docker-compose down -v
```

### 3. Development Workflow
```bash
# Start services without cleanup
./scripts/test-docker-compose.sh --no-cleanup

# Monitor in another terminal
./scripts/monitor-cluster.sh --compact

# Run specific tests
./scripts/test-message-distribution.sh --broadcast-only
```

## Test Scenarios Covered

### Cluster Formation
- ✅ Service startup and health checks
- ✅ UDP broadcast discovery
- ✅ Peer discovery and registration
- ✅ NATS cluster route formation
- ✅ JetStream initialization

### Message Distribution
- ✅ Broadcast messages to all nodes
- ✅ Message reception verification
- ✅ Distribution efficiency analysis
- ✅ Per-node reception statistics

### System Resilience
- ✅ Continuous message sending under load
- ✅ Cluster stability monitoring
- ✅ Node health tracking
- ✅ Recovery from temporary failures

### API Functionality
- ✅ Health endpoint testing
- ✅ Cluster status endpoint
- ✅ Message sending endpoints
- ✅ Broadcast functionality

## Expected Test Results

### Successful Cluster Formation
- All nodes report "healthy" status
- Each node discovers N-1 peers (where N is total nodes)
- Cluster status shows proper peer connections
- All nodes receive broadcast messages

### Message Distribution Metrics
- **Distribution Efficiency**: >90% (messages received vs expected)
- **Node Coverage**: 100% (all nodes receive messages)
- **Success Rate**: >95% (successful message sends)

### Performance Expectations
- **Startup Time**: <60 seconds for 3 nodes
- **Discovery Time**: <30 seconds for full cluster formation
- **Message Latency**: <1 second for broadcast distribution

## Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check Docker daemon
   docker info
   
   # Check port conflicts
   netstat -tulpn | grep -E "(8080|4222|6222|7222)"
   
   # Check logs
   docker-compose logs app
   ```

2. **Cluster not forming**
   ```bash
   # Check UDP broadcast (port 7222)
   sudo netstat -ulpn | grep 7222
   
   # Check firewall settings
   sudo ufw status
   
   # Monitor discovery logs
   docker-compose logs app | grep -i discovery
   ```

3. **Messages not distributing**
   ```bash
   # Check JetStream status
   docker-compose logs app | grep -i jetstream
   
   # Verify NATS connections
   docker-compose logs app | grep -i "nats.*connect"
   
   # Test individual node APIs
   curl http://localhost:8080/health
   ```

### Debug Mode
Enable debug logging by setting `DEBUG=true` in docker-compose.yml:
```yaml
environment:
  - DEBUG=true
```

### Log Analysis
```bash
# Filter cluster-related logs
docker-compose logs app | grep -E "(cluster|peer|broadcast|discovery)"

# Monitor message flow
docker-compose logs -f app | grep -i "received.*message"

# Check for errors
docker-compose logs app | grep -i error
```

## Dependencies

### Required Tools
- `docker` and `docker-compose`
- `curl` for API testing
- `jq` for JSON parsing (recommended)
- `bash` 4.0+ for script execution

### Optional Tools
- `netstat` for port checking
- `watch` for continuous monitoring
- `grep` and `awk` for log analysis

## Configuration

### Environment Variables
Scripts respect these environment variables:
- `EXPECTED_REPLICAS`: Number of expected service replicas (default: 3)
- `WAIT_TIMEOUT`: Service startup timeout in seconds (default: 60)
- `MESSAGE_COUNT`: Number of test messages (default: 10)
- `TEST_DURATION`: Resilience test duration (default: 30)

### Docker Compose Configuration
The scripts work with the default docker-compose.yml configuration:
- Service name: `app`
- HTTP port range: `8080-8089`
- NATS client port range: `4222-4231`
- NATS cluster port range: `6222-6231`
- UDP broadcast port: `7222`

## Contributing

When adding new test scripts:
1. Follow the existing naming convention
2. Include colored output for better UX
3. Provide comprehensive help text
4. Handle errors gracefully
5. Update this README with new script documentation
