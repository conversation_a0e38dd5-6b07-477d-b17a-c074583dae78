#!/bin/bash

echo "Testing local multi-node setup..."

# Create data directories
mkdir -p /tmp/node1 /tmp/node2 /tmp/node3

# Kill any existing processes
pkill -f "go run cmd/server/main.go" || true
sleep 2

# Start node 1
echo "Starting node 1..."
CLUSTER_NAME=test-cluster \
JETSTREAM_STORE_DIR=/tmp/node1 \
HTTP_PORT=8081 \
NATS_CLIENT_PORT=4221 \
NATS_CLUSTER_PORT=6221 \
BROADCAST_PORT=7221 \
DEBUG=true \
go run cmd/server/main.go &
NODE1_PID=$!

sleep 3

# Start node 2
echo "Starting node 2..."
CLUSTER_NAME=test-cluster \
JETSTREAM_STORE_DIR=/tmp/node2 \
HTTP_PORT=8082 \
NATS_CLIENT_PORT=4222 \
NATS_CLUSTER_PORT=6222 \
BROADCAST_PORT=7221 \
DEBUG=true \
go run cmd/server/main.go &
NODE2_PID=$!

sleep 3

# Start node 3
echo "Starting node 3..."
CLUSTER_NAME=test-cluster \
JETSTREAM_STORE_DIR=/tmp/node3 \
HTTP_PORT=8083 \
NATS_CLIENT_PORT=4223 \
NATS_CLUSTER_PORT=6223 \
BROADCAST_PORT=7221 \
DEBUG=true \
go run cmd/server/main.go &
NODE3_PID=$!

echo "Waiting for nodes to start and discover each other..."
sleep 10

echo "Testing broadcast message..."
curl -X POST "http://localhost:8081/api/v1/broadcast" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello from test script!"}'

echo ""
echo "Waiting for message propagation..."
sleep 2

echo "Cluster status from node 1:"
curl -s "http://localhost:8081/cluster" | jq . || curl -s "http://localhost:8081/cluster"

echo ""
echo "Cluster status from node 2:"
curl -s "http://localhost:8082/cluster" | jq . || curl -s "http://localhost:8082/cluster"

echo ""
echo "Cluster status from node 3:"
curl -s "http://localhost:8083/cluster" | jq . || curl -s "http://localhost:8083/cluster"

echo ""
echo "Press Enter to stop all nodes..."
read

# Clean up
kill $NODE1_PID $NODE2_PID $NODE3_PID 2>/dev/null || true
echo "All nodes stopped."
